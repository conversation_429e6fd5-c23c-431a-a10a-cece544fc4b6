Metadata-Version: 2.4
Name: layer-crawling
Version: 0.1.0
Summary: Scalable court case crawling system for Google Cloud Run
Requires-Python: >=3.12
Requires-Dist: beautifulsoup4>=4.12.2
Requires-Dist: fastapi>=0.104.1
Requires-Dist: google-cloud-error-reporting>=1.9.3
Requires-Dist: google-cloud-logging>=3.8.0
Requires-Dist: google-cloud-secret-manager>=2.18.1
Requires-Dist: gspread-formatting>=1.1.2
Requires-Dist: gspread>=5.12.0
Requires-Dist: oauth2client>=4.1.3
Requires-Dist: pandas>=2.1.3
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: pyperclip>=1.8.2
Requires-Dist: python-dateutil>=2.8.2
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: pytz>=2023.3
Requires-Dist: requests>=2.31.0
Requires-Dist: selenium>=4.15.2
Requires-Dist: structlog>=23.2.0
Requires-Dist: uvicorn[standard]>=0.24.0
Provides-Extra: dev
Requires-Dist: black>=23.11.0; extra == 'dev'
Requires-Dist: httpx>=0.25.2; extra == 'dev'
Requires-Dist: mypy>=1.7.1; extra == 'dev'
Requires-Dist: pytest-asyncio>=0.21.1; extra == 'dev'
Requires-Dist: pytest-cov>=4.1.0; extra == 'dev'
Requires-Dist: pytest>=7.4.3; extra == 'dev'
Requires-Dist: ruff>=0.1.6; extra == 'dev'
