"""Google Sheets client service."""

import time
from typing import List, Dict, Any, Optional
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from gspread.exceptions import APIError, SpreadsheetNotFound, WorksheetNotFound
import pandas as pd
from datetime import datetime

from ..models.config import GoogleSheetsConfig
from ..utils.logger import get_logger
from ..utils.exceptions import GoogleSheetsError


class GoogleSheetsClient:
    """Service for interacting with Google Sheets."""
    
    def __init__(self, config: GoogleSheetsConfig, credentials: Dict[str, Any]):
        self.config = config
        self.credentials = credentials
        self.logger = get_logger(self.__class__.__name__)
        self._client: Optional[gspread.Client] = None
    
    @property
    def client(self) -> gspread.Client:
        """Get gspread client (lazy initialization)."""
        if self._client is None:
            try:
                scope = [
                    "https://spreadsheets.google.com/feeds",
                    "https://www.googleapis.com/auth/drive",
                    "https://www.googleapis.com/auth/spreadsheets"
                ]
                
                creds = ServiceAccountCredentials.from_json_keyfile_dict(
                    self.credentials, scope
                )
                self._client = gspread.authorize(creds)
                
                self.logger.info("Google Sheets client initialized")
                
            except Exception as e:
                self.logger.error(f"Failed to initialize Google Sheets client: {e}")
                raise GoogleSheetsError(f"Failed to initialize Google Sheets client: {e}") from e
        
        return self._client
    
    def get_or_create_spreadsheet(self, name: str) -> gspread.Spreadsheet:
        """
        Get existing spreadsheet or create new one.
        
        Args:
            name: Spreadsheet name
            
        Returns:
            gspread.Spreadsheet: Spreadsheet instance
            
        Raises:
            GoogleSheetsError: If operation fails
        """
        try:
            # Try to open existing spreadsheet
            try:
                spreadsheet = self.client.open(name)
                self.logger.debug(f"Opened existing spreadsheet: {name}")
                return spreadsheet
            except SpreadsheetNotFound:
                # Create new spreadsheet
                spreadsheet = self.client.create(name)
                # Share with anyone (or specific users as needed)
                spreadsheet.share(None, perm_type="anyone", role="writer")
                self.logger.info(f"Created new spreadsheet: {name}")
                return spreadsheet
                
        except Exception as e:
            self.logger.error(f"Failed to get/create spreadsheet {name}: {e}")
            raise GoogleSheetsError(f"Failed to get/create spreadsheet {name}: {e}") from e
    
    def get_or_create_worksheet(
        self, 
        spreadsheet: gspread.Spreadsheet, 
        name: str, 
        rows: int = 1000, 
        cols: int = 26
    ) -> gspread.Worksheet:
        """
        Get existing worksheet or create new one.
        
        Args:
            spreadsheet: Spreadsheet instance
            name: Worksheet name
            rows: Number of rows for new worksheet
            cols: Number of columns for new worksheet
            
        Returns:
            gspread.Worksheet: Worksheet instance
            
        Raises:
            GoogleSheetsError: If operation fails
        """
        try:
            # Try to get existing worksheet
            try:
                worksheet = spreadsheet.worksheet(name)
                self.logger.debug(f"Found existing worksheet: {name}")
                return worksheet
            except WorksheetNotFound:
                # Create new worksheet
                worksheet = spreadsheet.add_worksheet(title=name, rows=rows, cols=cols)
                self.logger.info(f"Created new worksheet: {name}")
                return worksheet
                
        except Exception as e:
            self.logger.error(f"Failed to get/create worksheet {name}: {e}")
            raise GoogleSheetsError(f"Failed to get/create worksheet {name}: {e}") from e
    
    def ensure_header(self, worksheet: gspread.Worksheet, headers: List[str]) -> None:
        """
        Ensure worksheet has proper headers.
        
        Args:
            worksheet: Worksheet instance
            headers: List of header names
            
        Raises:
            GoogleSheetsError: If operation fails
        """
        try:
            # Check if worksheet is empty or has no headers
            all_values = worksheet.get_all_values()
            if not all_values or not all_values[0]:
                # Add headers
                worksheet.append_row(headers)
                self.logger.info(f"Added headers to worksheet: {headers}")
            else:
                self.logger.debug("Worksheet already has headers")
                
        except Exception as e:
            self.logger.error(f"Failed to ensure headers: {e}")
            raise GoogleSheetsError(f"Failed to ensure headers: {e}") from e
    
    def append_data_with_timestamp(
        self, 
        worksheet: gspread.Worksheet, 
        data: List[Dict[str, Any]], 
        timestamp_column: str = "크롤링일시"
    ) -> None:
        """
        Append data to worksheet with automatic timestamp.
        
        Args:
            worksheet: Worksheet instance
            data: List of data dictionaries
            timestamp_column: Name of timestamp column
            
        Raises:
            GoogleSheetsError: If operation fails
        """
        if not data:
            self.logger.warning("No data to append")
            return
        
        try:
            # Get current timestamp
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M')
            
            # Convert data to rows with timestamp
            rows = []
            for item in data:
                # Create a copy and add timestamp
                row_data = item.copy()
                row_data[timestamp_column] = timestamp
                rows.append(list(row_data.values()))
            
            # Append rows in batches
            self._append_rows_in_batches(worksheet, rows)
            
            self.logger.info(f"Appended {len(rows)} rows to worksheet")
            
        except Exception as e:
            self.logger.error(f"Failed to append data with timestamp: {e}")
            raise GoogleSheetsError(f"Failed to append data with timestamp: {e}") from e
    
    def append_dataframe(
        self, 
        worksheet: gspread.Worksheet, 
        df: pd.DataFrame, 
        include_header: bool = False
    ) -> None:
        """
        Append pandas DataFrame to worksheet.
        
        Args:
            worksheet: Worksheet instance
            df: DataFrame to append
            include_header: Whether to include DataFrame headers
            
        Raises:
            GoogleSheetsError: If operation fails
        """
        if df.empty:
            self.logger.warning("DataFrame is empty, nothing to append")
            return
        
        try:
            # Convert DataFrame to list of lists
            if include_header:
                rows = [df.columns.tolist()] + df.values.tolist()
            else:
                rows = df.values.tolist()
            
            # Convert all values to strings to avoid type issues
            rows = [[str(cell) if cell is not None else "" for cell in row] for row in rows]
            
            # Append rows in batches
            self._append_rows_in_batches(worksheet, rows)
            
            self.logger.info(f"Appended DataFrame with {len(df)} rows to worksheet")
            
        except Exception as e:
            self.logger.error(f"Failed to append DataFrame: {e}")
            raise GoogleSheetsError(f"Failed to append DataFrame: {e}") from e
    
    def _append_rows_in_batches(self, worksheet: gspread.Worksheet, rows: List[List[Any]]) -> None:
        """
        Append rows to worksheet in batches with retry logic.
        
        Args:
            worksheet: Worksheet instance
            rows: List of rows to append
            
        Raises:
            GoogleSheetsError: If operation fails after retries
        """
        batch_size = self.config.batch_size
        
        for i in range(0, len(rows), batch_size):
            batch = rows[i:i + batch_size]
            
            # Retry logic with exponential backoff
            for attempt in range(self.config.retry_attempts):
                try:
                    worksheet.append_rows(
                        batch,
                        value_input_option="RAW",
                        insert_data_option="INSERT_ROWS",
                        table_range="A1"
                    )
                    self.logger.debug(f"Appended batch {i//batch_size + 1} ({len(batch)} rows)")
                    break
                    
                except APIError as e:
                    if attempt < self.config.retry_attempts - 1:
                        delay = self.config.retry_delay_seconds * (2 ** attempt)
                        self.logger.warning(
                            f"API error on attempt {attempt + 1}, retrying in {delay}s: {e}"
                        )
                        time.sleep(delay)
                    else:
                        self.logger.error(f"Failed to append batch after {self.config.retry_attempts} attempts: {e}")
                        raise GoogleSheetsError(f"Failed to append batch: {e}") from e
                except Exception as e:
                    self.logger.error(f"Unexpected error appending batch: {e}")
                    raise GoogleSheetsError(f"Unexpected error appending batch: {e}") from e
    
    def upload_public_defender_data(self, data: List[Dict[str, Any]], spreadsheet_name: str = "국선 법원 크롤링") -> None:
        """
        Upload public defender case data to Google Sheets.
        
        Args:
            data: List of case data dictionaries
            spreadsheet_name: Name of the spreadsheet
            
        Raises:
            GoogleSheetsError: If upload fails
        """
        try:
            spreadsheet = self.get_or_create_spreadsheet(spreadsheet_name)
            worksheet = self.get_or_create_worksheet(spreadsheet, "국선선정내역크롤링로그")
            
            # Define headers
            headers = [
                "재판부, 재판장", "선정일", "사건번호", "사건명", "피고인명",
                "성별, 나이", "구속여부", "보석여부", "기소일", "선고일", "선고결과", "업데이트 시점"
            ]
            
            self.ensure_header(worksheet, headers)
            self.append_data_with_timestamp(worksheet, data, "업데이트 시점")
            
            self.logger.info(f"Successfully uploaded {len(data)} public defender cases")
            
        except Exception as e:
            self.logger.error(f"Failed to upload public defender data: {e}")
            raise GoogleSheetsError(f"Failed to upload public defender data: {e}") from e
    
    def upload_electronic_litigation_data(
        self, 
        general_data: List[Dict[str, Any]], 
        progress_data: List[Dict[str, Any]],
        spreadsheet_id: str = "1y4V6DRBobKltlc5NpRsLHN90bucEsIHqZdFm1VqeATY"
    ) -> None:
        """
        Upload electronic litigation data to Google Sheets.
        
        Args:
            general_data: List of general case data dictionaries
            progress_data: List of progress data dictionaries
            spreadsheet_id: ID of the target spreadsheet
            
        Raises:
            GoogleSheetsError: If upload fails
        """
        try:
            # Open spreadsheet by ID
            spreadsheet = self.client.open_by_key(spreadsheet_id)
            
            # Upload general content
            if general_data:
                general_worksheet = self.get_or_create_worksheet(spreadsheet, "사건 일반내용 크롤링 로그")
                general_headers = ["사건번호", "전체문자열", "크롤링일시"]
                self.ensure_header(general_worksheet, general_headers)
                self.append_data_with_timestamp(general_data, general_worksheet, "크롤링일시")
            
            # Upload progress data
            if progress_data:
                progress_worksheet = self.get_or_create_worksheet(spreadsheet, "사건 진행내역 크롤링 로그")
                progress_headers = ["사건번호", "일자", "내용", "결과", "크롤링일시"]
                self.ensure_header(progress_worksheet, progress_headers)
                self.append_data_with_timestamp(progress_data, progress_worksheet, "크롤링일시")
            
            self.logger.info(
                f"Successfully uploaded electronic litigation data: "
                f"{len(general_data)} general, {len(progress_data)} progress entries"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to upload electronic litigation data: {e}")
            raise GoogleSheetsError(f"Failed to upload electronic litigation data: {e}") from e
