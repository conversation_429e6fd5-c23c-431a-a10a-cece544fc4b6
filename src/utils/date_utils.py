"""Date manipulation utilities."""

from datetime import datetime, timedelta
from typing import Tuple
import re


def calculate_date_range(days_back: int = 7, date_format: str = "%Y.%m.%d") -> Tuple[str, str]:
    """
    Calculate date range for crawling.
    
    Args:
        days_back: Number of days to go back from today
        date_format: Format string for dates
        
    Returns:
        tuple: (start_date, end_date) formatted as strings
    """
    today = datetime.today()
    start_date = today - timedelta(days=days_back)
    
    return start_date.strftime(date_format), today.strftime(date_format)


def normalize_date_string(date_str: str) -> str:
    """
    Normalize date string by removing extra whitespace and standardizing format.
    
    Args:
        date_str: Input date string
        
    Returns:
        str: Normalized date string
    """
    if not date_str:
        return ""
    
    # Remove extra whitespace
    normalized = re.sub(r'\s+', ' ', date_str.strip())
    
    # Try to parse and reformat common date patterns
    date_patterns = [
        (r'(\d{4})[.\-/](\d{1,2})[.\-/](\d{1,2})', r'\1.\2.\3'),
        (r'(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})', r'\3.\1.\2'),
    ]
    
    for pattern, replacement in date_patterns:
        match = re.search(pattern, normalized)
        if match:
            try:
                # Validate the date
                year, month, day = match.groups()
                if len(year) == 2:
                    year = f"20{year}"
                datetime(int(year), int(month), int(day))
                return re.sub(pattern, replacement, normalized)
            except ValueError:
                continue
    
    return normalized


def format_timestamp(dt: datetime = None, format_str: str = "%Y-%m-%d %H:%M") -> str:
    """
    Format datetime as string.
    
    Args:
        dt: Datetime object (defaults to now)
        format_str: Format string
        
    Returns:
        str: Formatted datetime string
    """
    if dt is None:
        dt = datetime.now()
    return dt.strftime(format_str)


def parse_korean_date(date_str: str) -> str:
    """
    Parse Korean date format and convert to standard format.
    
    Args:
        date_str: Korean date string (e.g., "2024년 1월 15일")
        
    Returns:
        str: Standardized date string (YYYY.MM.DD)
    """
    if not date_str:
        return ""
    
    # Pattern for Korean date format
    korean_pattern = r'(\d{4})년\s*(\d{1,2})월\s*(\d{1,2})일'
    match = re.search(korean_pattern, date_str)
    
    if match:
        year, month, day = match.groups()
        return f"{year}.{month.zfill(2)}.{day.zfill(2)}"
    
    return normalize_date_string(date_str)
