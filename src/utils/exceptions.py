"""Custom exceptions for the crawling system."""


class CrawlerError(Exception):
    """Base exception for crawler-related errors."""
    pass


class AuthenticationError(CrawlerError):
    """Raised when authentication fails."""
    pass


class WebDriverError(CrawlerError):
    """Raised when WebDriver operations fail."""
    pass


class ConfigurationError(CrawlerError):
    """Raised when configuration is invalid."""
    pass


class SecretManagerError(CrawlerError):
    """Raised when Secret Manager operations fail."""
    pass


class GoogleSheetsError(CrawlerError):
    """Raised when Google Sheets operations fail."""
    pass


class CrawlingError(CrawlerError):
    """Raised when crawling operations fail."""
    pass


class DataValidationError(CrawlerError):
    """Raised when data validation fails."""
    pass
