"""Public Defender crawler implementation."""

import time
import random
from typing import Dict, Any, List
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from .base import BaseCrawler
from ..models.case_data import PublicDefenderCase, CrawlResult
from ..models.config import PublicDefenderConfig, LawyerAccount
from ..services.webdriver_manager import WebDriverManager
from ..utils.logger import get_logger
from ..utils.exceptions import CrawlingError, AuthenticationError
from ..utils.date_utils import calculate_date_range


class PublicDefenderCrawler(BaseCrawler):
    """Crawler for Public Defender appointment system."""
    
    def __init__(self, config: PublicDefenderConfig, webdriver_manager: WebDriverManager):
        super().__init__(config.__dict__)
        self.config = config
        self.webdriver_manager = webdriver_manager
        self.logger = get_logger(self.__class__.__name__)
        self._current_account: LawyerAccount = None
    
    def authenticate(self, credentials: LawyerAccount) -> bool:
        """
        Authenticate with the public defender system.
        
        Args:
            credentials: Lawyer account credentials
            
        Returns:
            bool: True if authentication successful
            
        Raises:
            AuthenticationError: If authentication fails
        """
        try:
            self._current_account = credentials
            
            # Navigate to login page
            self.webdriver_manager.navigate_to(self.config.base_url)
            
            # Enter username
            username_field = self.webdriver_manager.wait_for_element(By.ID, "mf_ibx_userId")
            username_field.clear()
            username_field.send_keys(credentials.username)
            
            # Small delay to mimic human behavior
            time.sleep(random.uniform(0.5, 1.0))
            
            # Enter password
            password_field = self.webdriver_manager.wait_for_element(By.ID, "mf_ibx_userPw")
            password_field.clear()
            password_field.send_keys(credentials.password)
            
            time.sleep(random.uniform(0.5, 1.0))
            
            # Click login button
            login_button = self.webdriver_manager.wait_for_clickable(By.ID, "mf_btn_login")
            self.webdriver_manager.safe_click(login_button)
            
            # Wait for login to complete
            time.sleep(2.0)
            
            # Handle potential duplicate login dialog
            self._handle_duplicate_login_dialog()
            
            # Verify login success by checking for expected elements
            try:
                self.webdriver_manager.wait_for_element(
                    By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_rad_dayDvs_input_1", timeout=10
                )
                self.logger.info(f"Successfully authenticated as {credentials.username}")
                return True
            except TimeoutException:
                raise AuthenticationError(f"Login verification failed for {credentials.username}")
                
        except Exception as e:
            self.logger.error(f"Authentication failed for {credentials.username}: {e}")
            raise AuthenticationError(f"Authentication failed: {e}") from e
    
    def crawl_cases(self, date_range_days: int = None, **kwargs) -> CrawlResult:
        """
        Crawl public defender cases.
        
        Args:
            date_range_days: Number of days to look back (default from config)
            **kwargs: Additional parameters
            
        Returns:
            CrawlResult: Crawling results
        """
        try:
            if not self._current_account:
                raise CrawlingError("Not authenticated. Call authenticate() first.")
            
            days_back = date_range_days or self.config.default_date_range_days
            start_date, end_date = calculate_date_range(days_back)
            
            self.logger.info(f"Starting crawl for date range: {start_date} to {end_date}")
            
            # Set up search parameters
            self._setup_search_parameters(start_date, end_date)
            
            # Execute search
            self._execute_search()
            
            # Load all results with infinite scroll
            total_cases = self._load_all_results()
            
            # Extract case details
            cases = self._extract_case_details(total_cases)
            
            # Filter cases by assigned courts if specified
            if self._current_account.assigned_courts:
                cases = self._filter_by_assigned_courts(cases)
            
            self.logger.info(f"Successfully crawled {len(cases)} cases")
            
            return CrawlResult(
                success=True,
                cases=cases,
                crawl_timestamp=self._start_time
            )
            
        except Exception as e:
            self.logger.error(f"Crawling failed: {e}")
            return CrawlResult(
                success=False,
                error_message=str(e),
                crawl_timestamp=self._start_time
            )
    
    def cleanup(self) -> None:
        """Clean up resources."""
        if self.webdriver_manager:
            self.webdriver_manager.cleanup()
    
    def _handle_duplicate_login_dialog(self) -> None:
        """Handle duplicate login confirmation dialog."""
        try:
            # Wait a bit for potential dialog to appear
            time.sleep(1.0)
            
            # Try to find and click the confirmation button
            # Note: This replaces the pyautogui approach with proper element detection
            try:
                # Look for common dialog button patterns
                confirm_selectors = [
                    "//button[contains(text(), '확인')]",
                    "//input[@value='확인']",
                    "//button[contains(@class, 'confirm')]",
                    "//*[@id='confirm']"
                ]
                
                for selector in confirm_selectors:
                    try:
                        confirm_button = self.webdriver_manager.driver.find_element(By.XPATH, selector)
                        if confirm_button.is_displayed():
                            self.webdriver_manager.safe_click(confirm_button)
                            self.logger.info("Handled duplicate login dialog")
                            time.sleep(1.0)
                            return
                    except NoSuchElementException:
                        continue
                        
            except Exception as e:
                self.logger.debug(f"No duplicate login dialog found or handled: {e}")
                
        except Exception as e:
            self.logger.warning(f"Error handling duplicate login dialog: {e}")
    
    def _setup_search_parameters(self, start_date: str, end_date: str) -> None:
        """Set up search parameters for case lookup."""
        try:
            # Click on appointment date radio button
            appointment_date_radio = self.webdriver_manager.wait_for_clickable(
                By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_rad_dayDvs_input_1"
            )
            self.webdriver_manager.safe_click(appointment_date_radio)
            
            time.sleep(1.0)
            
            # Set date range
            start_input = self.webdriver_manager.wait_for_element(
                By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_cal_bgngYmd_input"
            )
            end_input = self.webdriver_manager.wait_for_element(
                By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_cal_endYmd_input"
            )
            
            # Clear and set dates using ActionChains
            action_chains = self.webdriver_manager.action_chains
            
            # Set start date
            action_chains.move_to_element(start_input).click().perform()
            start_input.clear()
            action_chains.send_keys(start_date).perform()
            
            time.sleep(0.5)
            
            # Set end date
            action_chains.move_to_element(end_input).click().perform()
            end_input.clear()
            action_chains.send_keys(end_date).perform()
            
            self.logger.info(f"Set search date range: {start_date} to {end_date}")
            
        except Exception as e:
            self.logger.error(f"Failed to setup search parameters: {e}")
            raise CrawlingError(f"Failed to setup search parameters: {e}") from e
    
    def _execute_search(self) -> None:
        """Execute the search query."""
        try:
            search_button = self.webdriver_manager.wait_for_clickable(
                By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_btn_search"
            )
            self.webdriver_manager.safe_click(search_button)
            
            # Wait for results to load
            time.sleep(2.0)
            
            # Wait for the results table to be present
            self.webdriver_manager.wait_for_element(
                By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_body_table"
            )
            
            self.logger.info("Search executed successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to execute search: {e}")
            raise CrawlingError(f"Failed to execute search: {e}") from e
    
    def _load_all_results(self) -> int:
        """Load all results using infinite scroll."""
        try:
            wrapper_id = "mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_body_table"
            row_selector = f"#{wrapper_id} tr.grid_body_row"
            scroll_div = self.webdriver_manager.driver.find_element(
                By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_dataLayer"
            )
            
            last_count = -1
            while True:
                rows_now = self.webdriver_manager.driver.find_elements(By.CSS_SELECTOR, row_selector)
                cur_count = len(rows_now)
                
                if cur_count == last_count:
                    break
                    
                last_count = cur_count
                self.webdriver_manager.driver.execute_script(
                    "arguments[0].scrollTop = arguments[0].scrollHeight", scroll_div
                )
                time.sleep(0.6)
            
            self.logger.info(f"Loaded {last_count} total cases")
            return last_count
            
        except Exception as e:
            self.logger.error(f"Failed to load all results: {e}")
            raise CrawlingError(f"Failed to load all results: {e}") from e

    def _extract_case_details(self, total_cases: int) -> List[PublicDefenderCase]:
        """Extract detailed information for each case."""
        cases = []

        # Labels for case information
        labels = [
            "재판부, 재판장", "선정일", "사건번호", "사건명", "피고인명",
            "성별, 나이", "구속여부", "보석여부", "기소일", "선고일", "선고결과"
        ]

        for i in range(total_cases):
            try:
                self.logger.debug(f"Processing case {i+1}/{total_cases}")

                # Click on case link
                link_id = f"mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_link_{i}_3"
                case_link = self.webdriver_manager.wait_for_clickable(By.ID, link_id)
                self.webdriver_manager.safe_click(case_link, use_javascript=True)

                # Wait for detail page to load
                self.webdriver_manager.wait_for_element(
                    By.XPATH, "//th[span[text()='재판부, 재판장']]", timeout=15
                )
                time.sleep(1.0)

                # Extract case information
                case_data = {}
                for label in labels:
                    try:
                        th_element = self.webdriver_manager.driver.find_element(
                            By.XPATH, f"//th[span[text()='{label}']]"
                        )
                        td_element = th_element.find_element(By.XPATH, "following-sibling::td[1]")
                        case_data[label] = td_element.text.strip()
                    except NoSuchElementException:
                        case_data[label] = "Not Found"
                        self.logger.warning(f"Field not found: {label}")

                # Create PublicDefenderCase object
                case = PublicDefenderCase(
                    court_info=case_data.get("재판부, 재판장", ""),
                    appointment_date=case_data.get("선정일", ""),
                    case_number=case_data.get("사건번호", ""),
                    case_name=case_data.get("사건명", ""),
                    defendant_name=case_data.get("피고인명", ""),
                    gender_age=case_data.get("성별, 나이", ""),
                    detention_status=case_data.get("구속여부", ""),
                    bail_status=case_data.get("보석여부", ""),
                    indictment_date=case_data.get("기소일", ""),
                    sentence_date=case_data.get("선고일", ""),
                    sentence_result=case_data.get("선고결과", "")
                )

                cases.append(case)
                self.logger.debug(f"Extracted case: {case.case_number}")

                # Return to list
                return_button = self.webdriver_manager.wait_for_clickable(
                    By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_btn_retList"
                )
                self.webdriver_manager.safe_click(return_button)

                # Wait for list to reload
                self.webdriver_manager.wait_for_element(
                    By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_body_table"
                )
                time.sleep(0.5)

            except Exception as e:
                self.logger.error(f"Failed to process case {i+1}: {e}")
                # Try to return to list if we're stuck in detail view
                try:
                    return_button = self.webdriver_manager.driver.find_element(
                        By.ID, "mf_wfm_pkjMain_wfm_pkjMainTask_btn_retList"
                    )
                    self.webdriver_manager.safe_click(return_button)
                    time.sleep(1.0)
                except:
                    pass
                continue

        return cases

    def _filter_by_assigned_courts(self, cases: List[PublicDefenderCase]) -> List[PublicDefenderCase]:
        """Filter cases by lawyer's assigned courts."""
        if not self._current_account.assigned_courts:
            return cases

        filtered_cases = []
        assigned_courts = [court.strip() for court in self._current_account.assigned_courts]

        for case in cases:
            court_info = case.court_info.strip()

            # Check if any assigned court is mentioned in the court info
            for assigned_court in assigned_courts:
                if assigned_court in court_info:
                    filtered_cases.append(case)
                    break

        self.logger.info(
            f"Filtered {len(cases)} cases to {len(filtered_cases)} cases "
            f"for assigned courts: {assigned_courts}"
        )

        return filtered_cases
