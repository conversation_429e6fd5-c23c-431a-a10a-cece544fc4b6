"""Base crawler abstract class."""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from ..models.case_data import CrawlResult
from ..utils.logger import get_logger


class BaseCrawler(ABC):
    """Abstract base class for all crawlers."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the crawler with configuration."""
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self._driver = None
        self._start_time = None
    
    @abstractmethod
    def authenticate(self, credentials: Dict[str, Any]) -> bool:
        """
        Authenticate with the target system.
        
        Args:
            credentials: Authentication credentials
            
        Returns:
            bool: True if authentication successful, False otherwise
        """
        pass
    
    @abstractmethod
    def crawl_cases(self, **kwargs) -> CrawlResult:
        """
        Crawl cases from the system.
        
        Args:
            **kwargs: Additional parameters for crawling
            
        Returns:
            CrawlResult: Result of the crawling operation
        """
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """Clean up resources (WebDriver, connections, etc.)."""
        pass
    
    def __enter__(self):
        """Context manager entry."""
        self._start_time = datetime.now()
        self.logger.info("Starting crawler", extra={"crawler": self.__class__.__name__})
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        try:
            self.cleanup()
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
        
        if self._start_time:
            duration = datetime.now() - self._start_time
            self.logger.info(
                "Crawler finished",
                extra={
                    "crawler": self.__class__.__name__,
                    "duration_seconds": duration.total_seconds(),
                    "success": exc_type is None
                }
            )
    
    def _calculate_date_range(self, days_back: int = 7) -> tuple[str, str]:
        """
        Calculate date range for crawling.
        
        Args:
            days_back: Number of days to go back from today
            
        Returns:
            tuple: (start_date, end_date) in YYYY.MM.DD format
        """
        today = datetime.today()
        start_date = today - timedelta(days=days_back)
        
        fmt = "%Y.%m.%d"
        return start_date.strftime(fmt), today.strftime(fmt)
    
    def _retry_operation(self, operation, max_retries: int = 3, delay: float = 1.0):
        """
        Retry an operation with exponential backoff.
        
        Args:
            operation: Function to retry
            max_retries: Maximum number of retries
            delay: Initial delay between retries
            
        Returns:
            Result of the operation
            
        Raises:
            Exception: Last exception if all retries fail
        """
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return operation()
            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    wait_time = delay * (2 ** attempt)
                    self.logger.warning(
                        f"Operation failed, retrying in {wait_time}s",
                        extra={
                            "attempt": attempt + 1,
                            "max_retries": max_retries,
                            "error": str(e)
                        }
                    )
                    import time
                    time.sleep(wait_time)
                else:
                    self.logger.error(
                        f"Operation failed after {max_retries} retries",
                        extra={"error": str(e)}
                    )
        
        raise last_exception
