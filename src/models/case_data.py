"""Data models for case information."""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Dict, Any


@dataclass
class PublicDefenderCase:
    """Data model for public defender case information."""
    court_info: str
    appointment_date: str
    case_number: str
    case_name: str
    defendant_name: str
    gender_age: str
    detention_status: str
    bail_status: str
    indictment_date: str
    sentence_date: str
    sentence_result: str
    crawl_timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Google Sheets upload."""
        return {
            "재판부, 재판장": self.court_info,
            "선정일": self.appointment_date,
            "사건번호": self.case_number,
            "사건명": self.case_name,
            "피고인명": self.defendant_name,
            "성별, 나이": self.gender_age,
            "구속여부": self.detention_status,
            "보석여부": self.bail_status,
            "기소일": self.indictment_date,
            "선고일": self.sentence_date,
            "선고결과": self.sentence_result,
            "업데이트 시점": self.crawl_timestamp.strftime('%Y.%m.%d %H:%M')
        }


@dataclass
class ProgressEntry:
    """Data model for case progress entry."""
    date: str
    content: str
    result: str
    
    def to_dict(self, case_number: str, crawl_timestamp: datetime) -> Dict[str, Any]:
        """Convert to dictionary for Google Sheets upload."""
        return {
            "사건번호": case_number,
            "일자": self.date,
            "내용": self.content,
            "결과": self.result,
            "크롤링일시": crawl_timestamp.strftime('%Y-%m-%d %H:%M')
        }


@dataclass
class ElectronicLitigationCase:
    """Data model for electronic litigation case information."""
    case_number: str
    general_content: str
    progress_entries: List[ProgressEntry] = field(default_factory=list)
    crawl_timestamp: datetime = field(default_factory=datetime.now)
    
    def to_general_dict(self) -> Dict[str, Any]:
        """Convert general content to dictionary for Google Sheets upload."""
        return {
            "사건번호": self.case_number,
            "전체문자열": self.general_content,
            "크롤링일시": self.crawl_timestamp.strftime('%Y-%m-%d %H:%M')
        }
    
    def to_progress_dicts(self) -> List[Dict[str, Any]]:
        """Convert progress entries to list of dictionaries for Google Sheets upload."""
        return [
            entry.to_dict(self.case_number, self.crawl_timestamp)
            for entry in self.progress_entries
        ]


@dataclass
class CrawlResult:
    """Result of a crawling operation."""
    success: bool
    cases: List[Any] = field(default_factory=list)
    error_message: Optional[str] = None
    crawl_timestamp: datetime = field(default_factory=datetime.now)
    
    @property
    def case_count(self) -> int:
        """Number of cases crawled."""
        return len(self.cases)
