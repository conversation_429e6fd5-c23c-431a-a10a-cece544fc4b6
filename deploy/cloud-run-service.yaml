apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: court-crawling-system
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Resource allocation
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "2"
        run.googleapis.com/execution-environment: gen2
        
        # Scaling
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"
        
        # Timeout (15 minutes for long crawling operations)
        run.googleapis.com/timeout: "900s"
        
        # Concurrency (1 to avoid resource conflicts)
        run.googleapis.com/cpu-throttling: "false"
        
    spec:
      containerConcurrency: 1
      timeoutSeconds: 900
      containers:
      - image: gcr.io/PROJECT_ID/court-crawling-system:latest
        ports:
        - containerPort: 8080
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: WEBDRIVER_HEADLESS
          value: "true"
        - name: LOG_LEVEL
          value: "INFO"
        - name: LOG_FORMAT
          value: "json"
        - name: PORT
          value: "8080"
        - name: HOST
          value: "0.0.0.0"
        
        resources:
          limits:
            memory: "2Gi"
            cpu: "2000m"
          requests:
            memory: "1Gi"
            cpu: "1000m"
            
        # Health check
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          
      serviceAccountName: court-crawling-sa
