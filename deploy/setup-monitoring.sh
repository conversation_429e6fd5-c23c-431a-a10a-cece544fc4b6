#!/bin/bash

# Setup script for monitoring and alerting
# Run this script to configure monitoring for the court crawling system

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"your-project-id"}
REGION=${REGION:-"asia-northeast3"}
SERVICE_NAME="court-crawling-system"
NOTIFICATION_EMAIL=${NOTIFICATION_EMAIL:-"<EMAIL>"}

echo "Setting up monitoring and alerting for project: $PROJECT_ID"
echo "Service: $SERVICE_NAME"
echo "Notification email: $NOTIFICATION_EMAIL"

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo "Error: gcloud CLI is not installed"
    exit 1
fi

# Set the project
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "Enabling required APIs..."
gcloud services enable monitoring.googleapis.com
gcloud services enable logging.googleapis.com
gcloud services enable clouderrorreporting.googleapis.com

# Create notification channel for email alerts
echo "Creating notification channel..."
cat > /tmp/notification-channel.json << EOF
{
  "type": "email",
  "displayName": "Court Crawling System Alerts",
  "description": "Email notifications for court crawling system alerts",
  "labels": {
    "email_address": "$NOTIFICATION_EMAIL"
  }
}
EOF

NOTIFICATION_CHANNEL=$(gcloud alpha monitoring channels create --channel-content-from-file=/tmp/notification-channel.json --format="value(name)")
rm /tmp/notification-channel.json

echo "Created notification channel: $NOTIFICATION_CHANNEL"

# Create alerting policy for service errors
echo "Creating error rate alerting policy..."
cat > /tmp/error-rate-policy.json << EOF
{
  "displayName": "Court Crawling System - High Error Rate",
  "documentation": {
    "content": "This policy monitors the error rate of the court crawling system and alerts when it exceeds 10% over a 5-minute period."
  },
  "conditions": [
    {
      "displayName": "Error rate condition",
      "conditionThreshold": {
        "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$SERVICE_NAME\" AND metric.type=\"run.googleapis.com/request_count\"",
        "aggregations": [
          {
            "alignmentPeriod": "300s",
            "perSeriesAligner": "ALIGN_RATE",
            "crossSeriesReducer": "REDUCE_SUM",
            "groupByFields": ["resource.labels.service_name"]
          }
        ],
        "comparison": "COMPARISON_GREATER_THAN",
        "thresholdValue": 0.1,
        "duration": "300s"
      }
    }
  ],
  "notificationChannels": ["$NOTIFICATION_CHANNEL"],
  "alertStrategy": {
    "autoClose": "1800s"
  }
}
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/error-rate-policy.json
rm /tmp/error-rate-policy.json

# Create alerting policy for service availability
echo "Creating availability alerting policy..."
cat > /tmp/availability-policy.json << EOF
{
  "displayName": "Court Crawling System - Service Down",
  "documentation": {
    "content": "This policy monitors the availability of the court crawling system and alerts when the service is down."
  },
  "conditions": [
    {
      "displayName": "Service availability condition",
      "conditionThreshold": {
        "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$SERVICE_NAME\" AND metric.type=\"run.googleapis.com/request_count\"",
        "aggregations": [
          {
            "alignmentPeriod": "300s",
            "perSeriesAligner": "ALIGN_RATE",
            "crossSeriesReducer": "REDUCE_SUM",
            "groupByFields": ["resource.labels.service_name"]
          }
        ],
        "comparison": "COMPARISON_LESS_THAN",
        "thresholdValue": 0.01,
        "duration": "600s"
      }
    }
  ],
  "notificationChannels": ["$NOTIFICATION_CHANNEL"],
  "alertStrategy": {
    "autoClose": "1800s"
  }
}
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/availability-policy.json
rm /tmp/availability-policy.json

# Create alerting policy for memory usage
echo "Creating memory usage alerting policy..."
cat > /tmp/memory-policy.json << EOF
{
  "displayName": "Court Crawling System - High Memory Usage",
  "documentation": {
    "content": "This policy monitors memory usage of the court crawling system and alerts when it exceeds 80%."
  },
  "conditions": [
    {
      "displayName": "Memory usage condition",
      "conditionThreshold": {
        "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$SERVICE_NAME\" AND metric.type=\"run.googleapis.com/container/memory/utilizations\"",
        "aggregations": [
          {
            "alignmentPeriod": "300s",
            "perSeriesAligner": "ALIGN_MEAN",
            "crossSeriesReducer": "REDUCE_MEAN",
            "groupByFields": ["resource.labels.service_name"]
          }
        ],
        "comparison": "COMPARISON_GREATER_THAN",
        "thresholdValue": 0.8,
        "duration": "300s"
      }
    }
  ],
  "notificationChannels": ["$NOTIFICATION_CHANNEL"],
  "alertStrategy": {
    "autoClose": "1800s"
  }
}
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/memory-policy.json
rm /tmp/memory-policy.json

# Create log-based metric for crawling failures
echo "Creating log-based metric for crawling failures..."
gcloud logging metrics create crawling_failures \
    --description="Count of crawling failures" \
    --log-filter='resource.type="cloud_run_revision" AND resource.labels.service_name="'$SERVICE_NAME'" AND (textPayload:"crawling failed" OR textPayload:"Authentication failed" OR textPayload:"Failed to")' \
    --value-extractor="" || echo "Metric crawling_failures already exists"

# Create alerting policy for crawling failures
echo "Creating crawling failures alerting policy..."
cat > /tmp/crawling-failures-policy.json << EOF
{
  "displayName": "Court Crawling System - Crawling Failures",
  "documentation": {
    "content": "This policy monitors crawling failures and alerts when there are more than 3 failures in a 10-minute period."
  },
  "conditions": [
    {
      "displayName": "Crawling failures condition",
      "conditionThreshold": {
        "filter": "metric.type=\"logging.googleapis.com/user/crawling_failures\"",
        "aggregations": [
          {
            "alignmentPeriod": "600s",
            "perSeriesAligner": "ALIGN_RATE",
            "crossSeriesReducer": "REDUCE_SUM"
          }
        ],
        "comparison": "COMPARISON_GREATER_THAN",
        "thresholdValue": 3,
        "duration": "0s"
      }
    }
  ],
  "notificationChannels": ["$NOTIFICATION_CHANNEL"],
  "alertStrategy": {
    "autoClose": "3600s"
  }
}
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/crawling-failures-policy.json
rm /tmp/crawling-failures-policy.json

# Create uptime check
echo "Creating uptime check..."
cat > /tmp/uptime-check.json << EOF
{
  "displayName": "Court Crawling System Health Check",
  "monitoredResource": {
    "type": "uptime_url",
    "labels": {
      "project_id": "$PROJECT_ID",
      "host": "court-crawling-system-HASH-uc.a.run.app"
    }
  },
  "httpCheck": {
    "path": "/health",
    "port": 443,
    "useSsl": true,
    "validateSsl": true
  },
  "period": "300s",
  "timeout": "10s",
  "selectedRegions": ["USA", "EUROPE", "ASIA_PACIFIC"]
}
EOF

echo "Note: Update the host in uptime-check.json with your actual Cloud Run service URL"
echo "Then run: gcloud monitoring uptime create --config-from-file=/tmp/uptime-check.json"

# Create dashboard
echo "Creating monitoring dashboard..."
cat > /tmp/dashboard.json << EOF
{
  "displayName": "Court Crawling System Dashboard",
  "mosaicLayout": {
    "tiles": [
      {
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Request Count",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$SERVICE_NAME\" AND metric.type=\"run.googleapis.com/request_count\"",
                    "aggregation": {
                      "alignmentPeriod": "300s",
                      "perSeriesAligner": "ALIGN_RATE",
                      "crossSeriesReducer": "REDUCE_SUM"
                    }
                  }
                }
              }
            ]
          }
        }
      },
      {
        "width": 6,
        "height": 4,
        "xPos": 6,
        "widget": {
          "title": "Memory Utilization",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$SERVICE_NAME\" AND metric.type=\"run.googleapis.com/container/memory/utilizations\"",
                    "aggregation": {
                      "alignmentPeriod": "300s",
                      "perSeriesAligner": "ALIGN_MEAN",
                      "crossSeriesReducer": "REDUCE_MEAN"
                    }
                  }
                }
              }
            ]
          }
        }
      }
    ]
  }
}
EOF

gcloud monitoring dashboards create --config-from-file=/tmp/dashboard.json
rm /tmp/dashboard.json

echo "Monitoring and alerting setup completed successfully!"
echo ""
echo "Created:"
echo "- Email notification channel"
echo "- Error rate alerting policy"
echo "- Service availability alerting policy"
echo "- Memory usage alerting policy"
echo "- Crawling failures log-based metric and alerting policy"
echo "- Monitoring dashboard"
echo ""
echo "Next steps:"
echo "1. Update the uptime check configuration with your actual service URL"
echo "2. Test the alerting by triggering some errors"
echo "3. Customize the dashboard as needed"
echo "4. Set up additional notification channels (Slack, PagerDuty, etc.) if required"
