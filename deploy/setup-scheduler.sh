#!/bin/bash

# Setup script for Google Cloud Scheduler jobs
# Run this script to create scheduled crawling jobs

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"your-project-id"}
REGION=${REGION:-"asia-northeast3"}
SERVICE_URL=${SERVICE_URL:-"https://court-crawling-system-HASH-uc.a.run.app"}
API_KEY=${API_KEY:-"your-api-key-here"}

echo "Setting up Cloud Scheduler jobs for project: $PROJECT_ID"
echo "Service URL: $SERVICE_URL"

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo "Error: gcloud CLI is not installed"
    exit 1
fi

# Set the project
gcloud config set project $PROJECT_ID

# Enable Cloud Scheduler API
echo "Enabling Cloud Scheduler API..."
gcloud services enable cloudscheduler.googleapis.com

# Create App Engine app if it doesn't exist (required for Cloud Scheduler)
echo "Checking App Engine app..."
if ! gcloud app describe &>/dev/null; then
    echo "Creating App Engine app..."
    gcloud app create --region=$REGION
fi

# Create service account for Cloud Scheduler if it doesn't exist
echo "Creating Cloud Scheduler service account..."
gcloud iam service-accounts create cloud-scheduler-sa \
    --display-name="Cloud Scheduler Service Account" \
    --description="Service account for Cloud Scheduler jobs" || echo "Service account already exists"

# Grant Cloud Run Invoker role to the service account
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:cloud-scheduler-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/run.invoker"

# Job 1: Public Defender Crawling (Daily at 9 AM KST)
echo "Creating public defender crawling job..."
gcloud scheduler jobs create http public-defender-daily-crawl \
    --location=$REGION \
    --schedule="0 9 * * *" \
    --time-zone="Asia/Seoul" \
    --uri="$SERVICE_URL/crawl/public-defender" \
    --http-method=POST \
    --headers="Content-Type=application/json,Authorization=Bearer $API_KEY" \
    --message-body='{"date_range_days": 7}' \
    --oidc-service-account-email="cloud-scheduler-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --oidc-token-audience="$SERVICE_URL" \
    --max-retry-attempts=3 \
    --max-retry-duration=3600s \
    --min-backoff-duration=60s \
    --max-backoff-duration=300s || echo "Job public-defender-daily-crawl already exists"

# Job 2: Electronic Litigation Crawling (Daily at 10 AM KST)
echo "Creating electronic litigation crawling job..."
gcloud scheduler jobs create http electronic-litigation-daily-crawl \
    --location=$REGION \
    --schedule="0 10 * * *" \
    --time-zone="Asia/Seoul" \
    --uri="$SERVICE_URL/crawl/electronic-litigation" \
    --http-method=POST \
    --headers="Content-Type=application/json,Authorization=Bearer $API_KEY" \
    --message-body='{}' \
    --oidc-service-account-email="cloud-scheduler-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --oidc-token-audience="$SERVICE_URL" \
    --max-retry-attempts=3 \
    --max-retry-duration=3600s \
    --min-backoff-duration=60s \
    --max-backoff-duration=300s || echo "Job electronic-litigation-daily-crawl already exists"

# Job 3: Combined Crawling (Weekly on Mondays at 8 AM KST)
echo "Creating combined weekly crawling job..."
gcloud scheduler jobs create http combined-weekly-crawl \
    --location=$REGION \
    --schedule="0 8 * * 1" \
    --time-zone="Asia/Seoul" \
    --uri="$SERVICE_URL/crawl/all" \
    --http-method=POST \
    --headers="Content-Type=application/json,Authorization=Bearer $API_KEY" \
    --message-body='{"date_range_days": 14}' \
    --oidc-service-account-email="cloud-scheduler-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --oidc-token-audience="$SERVICE_URL" \
    --max-retry-attempts=3 \
    --max-retry-duration=3600s \
    --min-backoff-duration=60s \
    --max-backoff-duration=300s || echo "Job combined-weekly-crawl already exists"

echo "Cloud Scheduler setup completed successfully!"
echo ""
echo "Created jobs:"
echo "1. public-defender-daily-crawl - Daily at 9 AM KST"
echo "2. electronic-litigation-daily-crawl - Daily at 10 AM KST"
echo "3. combined-weekly-crawl - Weekly on Mondays at 8 AM KST"
echo ""
echo "To view jobs:"
echo "gcloud scheduler jobs list --location=$REGION"
echo ""
echo "To manually trigger a job:"
echo "gcloud scheduler jobs run JOB_NAME --location=$REGION"
echo ""
echo "To pause/resume a job:"
echo "gcloud scheduler jobs pause JOB_NAME --location=$REGION"
echo "gcloud scheduler jobs resume JOB_NAME --location=$REGION"
