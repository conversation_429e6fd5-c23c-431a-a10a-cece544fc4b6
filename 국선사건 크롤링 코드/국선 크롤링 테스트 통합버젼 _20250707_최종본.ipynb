{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e181c9b3", "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 85\u001b[39m\n\u001b[32m     81\u001b[39m replace_with(end_input,   today_str)  \u001b[38;5;66;03m# ② 종료일 = 오늘\u001b[39;00m\n\u001b[32m     83\u001b[39m ac.perform()\n\u001b[32m---> \u001b[39m\u001b[32m85\u001b[39m \u001b[43mtime\u001b[49m\u001b[43m.\u001b[49m\u001b[43msleep\u001b[49m\u001b[43m(\u001b[49m\u001b[43msleep_time\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     86\u001b[39m query = driver.find_element(By.ID, \u001b[33m\"\u001b[39m\u001b[33mmf_wfm_pkjMain_wfm_pkjMainTask_btn_search\u001b[39m\u001b[33m\"\u001b[39m) \u001b[38;5;66;03m#조회 버튼 클릭\u001b[39;00m\n\u001b[32m     87\u001b[39m query.click()\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains  #키보드 버튼 누르는 것 처럼 인식 시킬수 있는 옵션\n", "import time\n", "import random\n", "import pyautogui\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from datetime import datetime, timedelta #timedelta 특정날짜 조회를 위한 옵션\n", "import gspread\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "import pandas as pd\n", "from gspread_formatting import format_cell_range, CellFormat, Color\n", "\n", "options = Options()\n", "options.add_argument(\"--start-maximized\") # 시작시 최대창 출력\n", "#options.add_argument(\"user-data-dir=C:\\\\user_data\\\\kimhs\") #로그인 설정을 유지해 주는 옵션\n", "options.add_experimental_option(\"detach\", True) #셀레니움 자동종료 막는 옵션\n", "\n", "driver = webdriver.Chrome(options=options)\n", "action = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "\n", "sleep_time = random.uniform(1, 2.5)\n", "url = \"https://guksun.scourt.go.kr/pkj/index.on\" #국선 사이트 접속\n", "\n", "driver.get(url)\n", "query = driver.find_element(By.ID, \"mf_ibx_userId\") #아이디 입력\n", "query.send_keys(\"lawyerkk94\")\n", "\n", "time.sleep(sleep_time)\n", "query = driver.find_element(By.ID, \"mf_ibx_userPw\") #비밀번호 입력\n", "query.send_keys(\"kkcho0904!\")\n", "time.sleep(sleep_time)\n", "query = driver.find_element(By.ID, \"mf_btn_login\") #로그인 버튼 클릭\n", "query.click()\n", "time.sleep(2.0)\n", "\n", "#현재 상태 : 새로운 창이 떴을 때 요소 값을 찾을 수 없어서 강제로 마우스 클릭형태로 변형해서 사용 중\n", "# To-Do 화면해상도 관계없이 동일하게 적용할 수 있도록 개선 필요\n", "pyautogui.moveTo(1092, 741, 0.5) #중복 로그인 시 확인 버튼 클릭 \n", "pyautogui.click()\n", "time.sleep(sleep_time)\n", "\n", "query = driver.find_element(By.ID, \"mf_wfm_pkjMain_wfm_pkjMainTask_rad_dayDvs_input_1\") #선정일 버튼 클릭\n", "query.click()\n", "\n", "#선고일 버튼 클릭 추가 \n", "\n", "\n", "\n", "# 오늘, 7일전 날짜 계산 \n", "today = datetime.today()\n", "seven_days_ago = today - <PERSON><PERSON><PERSON>(days=7)\n", "\n", "# 화면에 맞춰 'YYYY.MM.DD' 형식으로 변환\n", "fmt = \"%Y.%m.%d\"\n", "today_str = today.strftime(fmt)          \n", "past7_str = seven_days_ago.strftime(fmt) \n", "\n", "time.sleep(2)\n", "\n", "# 날짜 입력창 요소 찾기\n", "start_input = driver.find_element(By.ID, \"mf_wfm_pkjMain_wfm_pkjMainTask_cal_bgngYmd_input\")  # 시작일 (과거 7일 전)\n", "end_input   = driver.find_element(By.ID, \"mf_wfm_pkjMain_wfm_pkjMainTask_cal_endYmd_input\")   # 종료일 (오늘)\n", "\n", "ac = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "\n", "def replace_with(element, text):\n", "    element.clear()                      # 기존 내용 삭제\n", "    ac.move_to_element(element)          \\\n", "      .click()                           \\\n", "      .send_keys(text)                   \\\n", "      .pause(0.1)                        # UI 반응 여유\n", "\n", "replace_with(start_input, past7_str)  # ① 시작일 = 7일 전\n", "replace_with(end_input,   today_str)  # ② 종료일 = 오늘\n", "\n", "ac.perform()\n", "\n", "time.sleep(sleep_time)\n", "query = driver.find_element(By.ID, \"mf_wfm_pkjMain_wfm_pkjMainTask_btn_search\") #조회 버튼 클릭\n", "query.click()\n", "\n", "time.sleep(0.5)\n", "\n", "# ─────────────────────────────────────────\n", "# 2) 목록 전체 로드 (무한 스크롤) 후 행 수 계산\n", "# ─────────────────────────────────────────\n", "wrapper_id   = \"mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_body_table\"   # 테이블 ID\n", "row_selector = f\"#{wrapper_id} tr.grid_body_row\"              # 실제 데이터 행)\n", "scroll_div   = driver.find_element(By.ID, \"mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_dataLayer\")  # 스크롤 바 div\n", "\n", "# 2‑1) 테이블 자체 로드 대기\n", "WebDriverWait(driver, 15).until(\n", "    EC.presence_of_element_located((By.ID, wrapper_id))\n", ")\n", "\n", "# 2‑2) 스크롤을 맨 끝까지 내려가며 전체 행을 모두 로드\n", "last_count = -1\n", "while True:\n", "    rows_now = driver.find_elements(By.CSS_SELECTOR, row_selector)\n", "    cur_count = len(rows_now)\n", "    if cur_count == last_count:        # 더 이상 증가 없음 → 스크롤 완료\n", "        break\n", "    last_count = cur_count\n", "    driver.execute_script(\"arguments[0].scrollTop = arguments[0].scrollHeight\", scroll_div)\n", "    time.sleep(0.6)  # 데이터 추가 로드 대기\n", "\n", "row_count = last_count\n", "print(f\"📋 스크롤 완료 후 전체 행 수: {row_count}\")\n", "\n", "# ─────────────────────────────────────────\n", "# 3) 사건별 상세 정보 수집\n", "# ─────────────────────────────────────────\n", "labels = [\n", "    \"재판부, 재판장\", \"선정일\", \"사건번호\", \"사건명\", \"피고인명\",\n", "    \"성별, 나이\", \"구속여부\", \"보석여부\", \"기소일\", \"선고일\", \"선고결과\"\n", "]\n", "all_results = []\n", "\n", "for i in range(row_count):\n", "    try:\n", "        link_id = f\"mf_wfm_pkjMain_wfm_pkjMainTask_grd_list_link_{i}_3\"\n", "        query = WebDriverWait(driver, 15).until(EC.element_to_be_clickable((By.ID, link_id)))\n", "        driver.execute_script(\"arguments[0].click();\", query)\n", "        print(f\"[{i+1}] 사건 클릭 완료\")\n", "\n", "        # 상세페이지 로딩 대기\n", "        WebDriverWait(driver, 15).until(\n", "            EC.presence_of_element_located((By.XPATH, \"//th[span[text()='재판부, 재판장']]\"))\n", "        )\n", "        time.sleep(1)  # 상세 페이지 렌더링 여유\n", "\n", "        result = {}\n", "        for label in labels:\n", "            try:\n", "                th = driver.find_element(By.XPATH, f\"//th[span[text()='{label}']]\")\n", "                td = th.find_element(By.XPATH, \"following-sibling::td[1]\")\n", "                result[label] = td.text.strip()\n", "            except:\n", "                result[label] = \"Not Found\"\n", "\n", "        all_results.append(result)\n", "        print(f\"[{i+1}] 사건 정보 저장 완료\")\n", "\n", "        # 목록으로 복귀\n", "        driver.find_element(By.ID, \"mf_wfm_pkjMain_wfm_pkjMainTask_btn_retList\").click()\n", "        WebDriverWait(driver, 15).until(\n", "            EC.presence_of_element_located((By.ID, wrapper_id))\n", "        )\n", "        time.sleep(0.5)\n", "\n", "    except Exception as e:\n", "        print(f\"[{i+1}] 처리 실패: {e}\")\n", "        continue\n", " \n", "# 크롤링 결과 저장\n", "now = datetime.now().strftime(\"%Y.%m.%d_%H:%M\")\n", "# 1) DataFrame 만들기\n", "df = pd.DataFrame(all_results)\n", "# 2) CSV로 저장\n", "csv_filename = f\"법원_데이터_결과_{now}.csv\"\n", "df.to_csv(csv_filename, index=False, encoding=\"utf-8-sig\")  # Excel에서 한글 깨짐 방지용 BOM\n", "print(f\"결과 저장 완료: {csv_filename}\")\n", "\n", "driver.quit()\n", "\n", "# ------------------ 구글 시트 연결 ------------------\n", "scope = [\n", "    \"https://spreadsheets.google.com/feeds\",\n", "    \"https://www.googleapis.com/auth/drive\",\n", "]\n", "creds  = ServiceAccountCredentials.from_json_keyfile_name(\"credentials.json\", scope)\n", "client = gspread.authorize(creds)\n", "\n", "sheet_name = \"국선 법원 크롤링\"\n", "try:\n", "    ss = client.open(sheet_name)\n", "except gspread.SpreadsheetNotFound:\n", "    ss = client.create(sheet_name)\n", "    ss.share(None, perm_type=\"anyone\", role=\"writer\")\n", "\n", "# ------------------ 워크시트 선택/생성 ------------------\n", "try:\n", "    ws = ss.worksheet(\"국선선정내역크롤링로그\")\n", "except gspread.exceptions.WorksheetNotFound:\n", "    ws = ss.add_worksheet(title=\"국선선정내역크롤링로그\", rows=\"100\", cols=\"20\")\n", "\n", "# ------------------ 1. DataFrame 정리 ------------------\n", "TIMESTAMP_COL = '업데이트 시점'   # ← 이미 들어 와 있을 수 있는 열 이름\n", "if TIMESTAMP_COL in df.columns:\n", "    df = df.drop(columns=[TIMESTAMP_COL])   # ★ 중복 타임스탬프 제거\n", "\n", "# ------------------ 2. 타임스탬프를 쓸 열 위치 ------------------\n", "TARGET_COL_INDEX = 12         # L열 (A=1 … L=11)\n", "\n", "# ------------------ 3. 헤더 생성 (없을 때만) ------------------\n", "if not ws.get_all_values():  # 시트가 비어 있는 경우\n", "    padding = [''] * (TARGET_COL_INDEX - len(df.columns) - 1)\n", "    header  = df.columns.tolist() + padding + [TIMESTAMP_COL]\n", "    ws.append_row(header)\n", "\n", "# ------------------ 4. 데이터 + 타임스탬프 준비 ------------------\n", "timestamp = datetime.now().strftime('%Y.%m.%d %H:%M')\n", "\n", "rows_with_ts = [\n", "    row + [''] * (TARGET_COL_INDEX - len(row) - 1) + [timestamp]\n", "    for row in df.values.tolist()\n", "]\n", "\n", "# ------------------ 5. 시트 하단에 A열부터 일괄 추가 ------------------\n", "ws.append_rows(\n", "    rows_with_ts,\n", "    value_input_option=\"RAW\",\n", "    insert_data_option=\"INSERT_ROWS\",  # 하단에 삽입\n", "    table_range=\"A1\"                   # ★ A열 고정\n", ")\n", "\n", "print(\"구글 시트 반영 완료 (타임스탬프 → L열)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}