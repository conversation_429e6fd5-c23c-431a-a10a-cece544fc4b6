# 최종 테스트 완료
# # -*- coding: utf-8 -*-
"""
전자소송 크롤링 - 관심사건 일괄 수집(일반내용 전체문자열 + 진행내용)
- 로그인: 탭 클릭 → ID 입력 → 가상 키패드로 비밀번호 입력 → 로그인
- 일반내용: 컨테이너 탐색(JS) → 전체문자열 저장, 사건번호는 목록 a태그에서 우선 확보
- 진행내용: 탭 클릭 → 테이블에서 '일자','내용','결과' 추출
- 결과 저장: 구글 스프레드시트 2시트 업로드
    · 사건 일반내용 크롤링 로그  : [사건번호, 전체문자열, 크롤링일시]   ← 마지막 열 자동 주입
    · 사건 진행내역 크롤링 로그  : [사건번호, 일자, 내용, 결과, 크롤링일시] ← 마지막 열 자동 주입
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import time, re, pyperclip, pandas as pd
from datetime import datetime

# ===== 구글 스프레드시트 업로드 유틸 =====
import gspread
from oauth2client.service_account import ServiceAccountCredentials

SPREADSHEET_ID = "1y4V6DRBobKltlc5NpRsLHN90bucEsIHqZdFm1VqeATY"
WS_PROG = "사건 진행내역 크롤링 로그"
WS_GEN  = "사건 일반내용 크롤링 로그"

# 업로드 템플릿 (마지막 열 '크롤링일시'는 업로드 시 자동 입력)
TEMPLATE_GEN  = ["사건번호", "전체문자열", "크롤링일시"]
TEMPLATE_PROG = ["사건번호", "일자", "내용", "결과", "크롤링일시"]
TS_COL_NAME   = "크롤링일시"

def _now_ts() -> str:
    return datetime.now().strftime('%Y-%m-%d %H:%M')

def get_client():
    creds  = ServiceAccountCredentials.from_json_keyfile_name("credentials.json", [
        "https://www.googleapis.com/auth/spreadsheets",
        "https://www.googleapis.com/auth/drive",
    ])
    return gspread.authorize(creds)

def _open_ss_by_id(client, key: str):
    try:
        return client.open_by_key(key)
    except gspread.SpreadsheetNotFound as e:
        raise RuntimeError("스프레드시트를 ID로 열 수 없습니다. "
                           "ID가 맞는지, 서비스계정이 편집자로 공유되었는지 확인하세요.") from e

def _open_or_create_ws(ss, title: str, rows="1000", cols="26"):
    try:
        return ss.worksheet(title)
    except gspread.exceptions.WorksheetNotFound:
        return ss.add_worksheet(title=title, rows=rows, cols=cols)

def _ensure_header(ws, template_cols: list[str]):
    if ws.get_all_values():
        return
    ws.append_row(template_cols)

def _df_to_rows_with_ts(df: pd.DataFrame, template_cols: list[str]) -> list[list[str]]:
    """
    템플릿 마지막 열이 TS_COL_NAME(=크롤링일시)이면 업로드 시 자동 주입.
    → DF에는 '크롤링일시' 컬럼이 없어야 정상 동작(마지막 열은 우리가 채움).
    """
    rows: list[list[str]] = []
    is_ts_last = (template_cols and template_cols[-1] == TS_COL_NAME)
    for _, r in df.iterrows():
        ordered = []
        for idx, col in enumerate(template_cols):
            if is_ts_last and idx == len(template_cols) - 1:
                ordered.append(_now_ts())  # 자동 타임스탬프
            else:
                ordered.append("" if col not in df.columns or pd.isna(r.get(col, "")) else str(r[col]))
        rows.append(ordered)
    return rows

def _append_rows(ws, rows: list[list[str]], batch_size: int = 500):
    if not rows:
        return
    for start in range(0, len(rows), batch_size):
        batch = rows[start:start+batch_size]
        attempt, delay = 0, 1.0
        while True:
            try:
                ws.append_rows(
                    batch,
                    value_input_option="RAW",
                    insert_data_option="INSERT_ROWS",
                    table_range="A1"
                )
                break
            except gspread.exceptions.APIError:
                attempt += 1
                if attempt > 5:
                    raise
                time.sleep(delay)
                delay = min(delay * 2, 16)

def upload_logs_to_sheets(df_prog: pd.DataFrame, df_gen: pd.DataFrame):
    """
    df_gen : [사건번호, 전체문자열]
    df_prog: [사건번호, 일자, 내용, 결과]
    (둘 다 '크롤링일시'는 템플릿 마지막 열 자동 입력)
    """
    client = get_client()
    ss = _open_ss_by_id(client, SPREADSHEET_ID)

    # 일반내용
    ws_gen = _open_or_create_ws(ss, WS_GEN)
    _ensure_header(ws_gen, TEMPLATE_GEN)
    rows_gen = _df_to_rows_with_ts(df_gen, TEMPLATE_GEN)
    _append_rows(ws_gen, rows_gen)

    # 진행내용
    ws_prog = _open_or_create_ws(ss, WS_PROG)
    _ensure_header(ws_prog, TEMPLATE_PROG)
    rows_prog = _df_to_rows_with_ts(df_prog, TEMPLATE_PROG)
    _append_rows(ws_prog, rows_prog)

    print("[✅] 구글 스프레드시트 업로드 완료 →", WS_GEN, "/", WS_PROG)


# ─────────────────────────────────────────────────────
# 환경 상수
# ─────────────────────────────────────────────────────
url_login   = "https://ecfs.scourt.go.kr/psp/index.on?m=PSP101M01"
tab_btn_id  = "mf_pfwork_tabctrl_tab_tabs2_tabHTML"      # '아이디 로그인' 탭
id_box_id   = "mf_pfwork_ibx_elpUserId"
pwd_box_id  = "mf_pfwork_ibx_elpUserPwd"
login_btnid = "mf_pfwork_btn_login"
keypad_div_id = f"nppfs-keypad-{pwd_box_id}"

user_id     = "lawyer87"
password    = "kkcho0904!"

CASE_ROWS_XPATH = "//table[contains(@class,'grid')]/tbody/tr"
CASE_ANCHOR_REL = ".//a[contains(@class,'link') or self::a]"

PROG_TAB_ID   = "mf_wfSsgoDetail_ssgoCsDetailTab_tab_ssgoTab2_tabHTML"
PROG_TABLE_ID = "mf_wfSsgoDetail_ssgoCsDetailTab_contents_ssgoTab2_body_grd_csProgLst_body_grd_csProgLst_body_table"

# 사건번호 패턴
CASE_NO_RE = re.compile(r'(?:19|20)\d{2}\s*[가-힣]{1,4}\s*\d{1,7}')

# ─────────────────────────────────────────────────────
# 셀레니움 기본 설정
# ─────────────────────────────────────────────────────
chrome_opts = Options()
chrome_opts.add_argument("--start-maximized")
chrome_opts.add_experimental_option("detach", True)
driver = webdriver.Chrome(options=chrome_opts)
wait   = WebDriverWait(driver, 10)
action = ActionChains(driver)

# ─────────────────────────────────────────────────────
# 가상 키패드 입력
# ─────────────────────────────────────────────────────
def _enter_pwd(pwd):
    try:
        iframe = driver.find_element(By.XPATH, f'//iframe[contains(@id,"{keypad_div_id}")]')
        driver.switch_to.frame(iframe)
    except:
        pass
    wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR,'div.kpd-group.lower')))
    for ch in pwd:
        label = ch if ch.isdigit() else (f"소문자 {ch}" if ch.isalpha() else "느낌표")
        if ch == '!':
            try:
                wait.until(EC.element_to_be_clickable((By.XPATH,'//img[@aria-label="느낌표"]')))
            except:
                toggle = driver.find_element(By.XPATH,'//div[contains(@class,"kpd-group lower")]//img[@aria-label="특수문자"]')
                driver.execute_script("arguments[0].click();", toggle)
                wait.until(EC.element_to_be_clickable((By.XPATH,'//img[@aria-label="느낌표"]')))
        btn = wait.until(EC.element_to_be_clickable((By.XPATH,f'//img[@aria-label="{label}"]')))
        driver.execute_script("arguments[0].click();", btn)
        time.sleep(0.03)
    for xp in (f'//*[@id="{keypad_div_id}"]/div/div[5]/img[39]', '//img[@data-action="action:enter"]'):
        try:
            btn = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.XPATH, xp)))
            driver.execute_script("arguments[0].click();", btn)
            break
        except:
            continue
    driver.switch_to.default_content()

# ─────────────────────────────────────────────────────
# 유틸/네비게이션
# ─────────────────────────────────────────────────────
def login():
    driver.get(url_login)
    wait.until(EC.element_to_be_clickable((By.ID, tab_btn_id))).click()
    time.sleep(0.8)
    wait.until(EC.element_to_be_clickable((By.ID, id_box_id))).send_keys(user_id)
    driver.find_element(By.ID, pwd_box_id).click()
    wait.until(EC.visibility_of_element_located((By.ID, keypad_div_id)))
    _enter_pwd(password)
    wait.until(EC.element_to_be_clickable((By.ID, login_btnid))).click()
    print("[✅] 로그인 완료")

def go_to_my_ecfs():
    btn = wait.until(EC.element_to_be_clickable((By.ID, "mf_pmf_content1_wq_uuid_286")))
    try: btn.click()
    except: driver.execute_script("arguments[0].click();", btn)
    wait.until(EC.any_of(
        EC.presence_of_element_located((By.ID, "mf_pfmenu_v3_li_a_150202")),
        EC.presence_of_element_located((By.XPATH, "//h2[contains(.,'나의 전자소송')]"))
    ))
    print("[✅] 나의 전자소송 클릭 완료")

def go_to_interest_case():
    time.sleep(0.5)
    driver.find_element(By.ID, "mf_pfmenu_v3_li_a_150202").click()
    driver.find_element(By.ID, "mf_pfwork_sbx_cortList").click()
    driver.find_element(By.XPATH, '//*[@id="mf_pfwork_sbx_cortList"]/option[2]').click()
    time.sleep(1.5)
    driver.find_element(By.ID, "mf_pfwork_btn_search").click()
    print("[✅] 관심사건 조회 완료")

# ─────────────────────────────────────────────────────
# 사건번호 파싱/정제
# ─────────────────────────────────────────────────────
def normalize_case_no(s: str) -> str:
    return re.sub(r"\s+", "", s or "").strip()

def pick_case_no_from_text(text: str) -> str:
    if not text:
        return ""
    m = CASE_NO_RE.search(text)
    if not m:
        return ""
    return normalize_case_no(m.group(0))

# ─────────────────────────────────────────────────────
# 목록 → 상세 (사건번호 함께 반환)
# ─────────────────────────────────────────────────────
def click_case_by_index(i: int) -> str:
    rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, CASE_ROWS_XPATH)))
    if i < 0 or i >= len(rows):
        raise IndexError("사건 인덱스 범위를 벗어났습니다.")
    anchor = rows[i].find_element(By.XPATH, CASE_ANCHOR_REL)
    case_no_from_list = normalize_case_no(anchor.text)
    driver.execute_script("arguments[0].click();", anchor)
    time.sleep(0.6)
    if len(driver.window_handles) > 1:
        driver.switch_to.window(driver.window_handles[-1])  # 헬퍼 없이 최신 창으로 전환

    def has_anchor_here():
        try:
            driver.find_element(By.XPATH, "//*[contains(normalize-space(.),'사건번호')]")
            return True
        except:
            return False

    if not has_anchor_here():
        driver.switch_to.default_content()
        frames = driver.find_elements(By.TAG_NAME, "iframe")
        for fr in frames:
            try:
                driver.switch_to.default_content()
                driver.switch_to.frame(fr)
                if has_anchor_here():
                    return case_no_from_list
            except:
                continue
        driver.switch_to.default_content()

    return case_no_from_list

# ─────────────────────────────────────────────────────
# 일반내용 추출(컨테이너 → 화면 전체 → 키입력 백업)
# ─────────────────────────────────────────────────────
def extract_general_text() -> str:
    def _try_here():
        try:
            anchor = wait.until(EC.presence_of_element_located(
                (By.XPATH, "//*[contains(normalize-space(.),'사건번호')]")
            ))
        except Exception:
            return ""
        try:
            txt = driver.execute_script("""
                const anchor = arguments[0];
                const ALLOWED = new Set(['DIV','SECTION','ARTICLE','MAIN','TABLE','TBODY','TR','TD','FORM']);
                let el = anchor;
                while (el && !ALLOWED.has(el.tagName)) el = el.parentElement;
                const cont = el || anchor;
                return (cont.innerText || '').trim();
            """, anchor) or ""
            if len(txt) >= 10:
                return txt
        except Exception:
            pass
        try:
            body = (driver.execute_script("return (document.body.innerText || '').trim();") or "")
            if len(body) >= 10:
                return body
        except Exception:
            pass
        return ""

    t = _try_here()
    if len(t) >= 10:
        return t

    try:
        driver.switch_to.default_content()
        frames = driver.find_elements(By.TAG_NAME, "iframe")
        for fr in frames:
            try:
                driver.switch_to.default_content()
                driver.switch_to.frame(fr)
                t = _try_here()
                if len(t) >= 10:
                    return t
            except Exception:
                continue
    finally:
        try: driver.switch_to.default_content()
        except: pass

    try:
        body_el = wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        body_el.click()
        time.sleep(0.2)
        action.key_down(Keys.CONTROL).send_keys("a").key_up(Keys.CONTROL).perform()
        time.sleep(0.2)
        action.key_down(Keys.CONTROL).send_keys("c").key_up(Keys.CONTROL).perform()
        time.sleep(0.2)
        from_text = (pyperclip.paste() or "").strip()
        if len(from_text) >= 10:
            return from_text
    except Exception:
        pass

    return ""

# ─────────────────────────────────────────────────────
# 진행내용 파싱
# ─────────────────────────────────────────────────────
def click_progress_tab_and_extract_table(case_no: str) -> list[dict]:
    out_rows = []
    try:
        tab = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.ID, PROG_TAB_ID)))
        try:
            tab.click()
        except:
            driver.execute_script("arguments[0].click();", tab)
        time.sleep(1.0)

        try:
            table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, PROG_TABLE_ID)))
        except:
            table = WebDriverWait(driver, 10).until(EC.presence_of_element_located(
                (By.XPATH, "//table[contains(@id,'grd_csProgLst') and contains(@id,'table')]")
            ))

        trs = table.find_elements(By.XPATH, ".//tr")
        for tr in trs:
            tds = tr.find_elements(By.XPATH, ".//td")
            if len(tds) < 3:
                continue
            date_txt = tds[0].text.strip()
            cont_txt = tds[1].text.strip()
            res_txt  = tds[2].text.strip()
            if not date_txt:
                continue
            out_rows.append({
                "사건번호": case_no,
                "일자": date_txt,
                "내용": cont_txt,
                "결과": res_txt
            })
    except Exception as e:
        print(f"[WARN] 진행내용 파싱 중 예외: {e}")
    return out_rows

# ─────────────────────────────────────────────────────
# 메인 크롤링
# ─────────────────────────────────────────────────────
def get_case_count() -> int:
    rows = wait.until(EC.presence_of_all_elements_located((By.XPATH, CASE_ROWS_XPATH)))
    return len(rows)

def crawl_all_cases_by_index():
    all_general_rows  = []
    all_progress_rows = []

    total = get_case_count()
    print(f"[INFO] 사건 건수: {total}건")

    for i in range(total):
        try:
            print(f"\n[DEBUG] {i+1}/{total}번째 사건 상세 진입 시도")
            case_no_from_list = click_case_by_index(i)

            full_text = extract_general_text()
            if not full_text:
                print("[WARN] 일반내용 텍스트 추출 실패 → 본 사건 스킵")
                if len(driver.window_handles) > 1:
                    driver.close(); driver.switch_to.window(driver.window_handles[0])
                time.sleep(0.5)
                continue

            case_no = pick_case_no_from_text(case_no_from_list) or pick_case_no_from_text(full_text) or case_no_from_list
            case_no = normalize_case_no(case_no)

            # 일반내용 행 (타임스탬프는 업로드에서 자동 주입)
            row_general = {
                "사건번호": case_no,
                "전체문자열": full_text,
            }
            all_general_rows.append(row_general)

            # 진행내용 행들 (타임스탬프는 업로드에서 자동 주입)
            prog_rows = click_progress_tab_and_extract_table(case_no)
            all_progress_rows.extend(prog_rows)

        except Exception as e:
            print(f"[WARN] 사건({i}) 처리 중 예외: {e}")
        finally:
            if len(driver.window_handles) > 1:
                driver.close(); driver.switch_to.window(driver.window_handles[0])
            time.sleep(0.5)

    # ── DataFrame 및 컬럼 순서 고정 ─────────────────
    df_general_out  = pd.DataFrame(all_general_rows)
    df_progress_out = pd.DataFrame(all_progress_rows)

    if not df_general_out.empty:
        desired_cols_gen = ["사건번호", "전체문자열"]  # '크롤링일시'는 템플릿 마지막 열 자동 주입
        df_general_out = df_general_out.reindex(columns=desired_cols_gen)

    if not df_progress_out.empty and "일자" in df_progress_out.columns:
        try:
            s = pd.to_datetime(df_progress_out["일자"], errors="coerce")
            df_progress_out.loc[s.notna(), "일자"] = s[s.notna()].dt.strftime("%Y-%m-%d")
            df_progress_out = df_progress_out.sort_values(["사건번호", "일자"], kind="stable")
        except Exception:
            pass

    # ── 구글 스프레드시트 업로드 ──
    if df_general_out.empty and df_progress_out.empty:
        print("\n[⚠️] 업로드할 데이터가 없습니다.")
    else:
        upload_logs_to_sheets(df_prog=df_progress_out, df_gen=df_general_out)

# ─────────────────────────────────────────────────────
# 실행
# ─────────────────────────────────────────────────────
if __name__ == "__main__":
    try:
        login()
        go_to_my_ecfs()
        go_to_interest_case()
        crawl_all_cases_by_index()
    finally:
        print("[DONE]")
        try:
            driver.quit()
        except:
            pass
