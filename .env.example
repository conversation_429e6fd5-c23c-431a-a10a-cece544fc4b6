# Environment Configuration
ENVIRONMENT=development
DEBUG=false

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id

# WebDriver Configuration
WEBDRIVER_HEADLESS=true
WEBDRIVER_WINDOW_WIDTH=1920
WEBDRIVER_WINDOW_HEIGHT=1080
WEBDRIVER_TIMEOUT_SECONDS=30

# Public Defender Crawler Configuration
PUBLIC_DEFENDER_BASE_URL=https://guksun.scourt.go.kr/pkj/index.on
PUBLIC_DEFENDER_DATE_RANGE_DAYS=7
PUBLIC_DEFENDER_MAX_RETRIES=3

# Electronic Litigation Crawler Configuration
ELECTRONIC_LITIGATION_BASE_URL=https://ecfs.scourt.go.kr/psp/index.on?m=PSP101M01
ELECTRONIC_LITIGATION_MAX_RETRIES=3

# Google Sheets Configuration
GOOGLE_SHEETS_BATCH_SIZE=500
GOOGLE_SHEETS_RETRY_ATTEMPTS=5

# Secret Manager Configuration
SECRET_MANAGER_LAWYER_ACCOUNTS=lawyer-accounts
SECRET_MANAGER_ELECTRONIC_LITIGATION=electronic-litigation-credentials
SECRET_MANAGER_GOOGLE_SHEETS=google-sheets-credentials

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# FastAPI Configuration
API_HOST=0.0.0.0
API_PORT=8080
API_KEY=your-api-key-here

# Cloud Run Configuration (for deployment)
PORT=8080
HOST=0.0.0.0
