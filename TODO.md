google cloud 에 배포 할 수 있는 형태로 변경

1. 아래 두 파일을 확장 가능한 구조의 python 코드로 리팩토링하여 google cloud run(job) 으로 배포
  * 국선 크롤링 테스트 통합버젼 _20250707_최종본.ipynb
  * 국선사건 크롤링 코드/국선 크롤링 테스트 통합버젼 _20250707_최종본.ipynb

2. 실행 방식
  - 앱시트에서 트리거 
  -  AppSheet 에서 webhook api 실행
  -  Cloud Scheduler 의 스케줄 실행

3. Google Cloud Secret Manager를 통한 비밀 정보 중앙 관리
  - [ ] 변호사 계정 목록
  - [ ] "국선사건 크롤링 코드/국선 크롤링 테스트 통합버젼 _20250707_최종본.ipynb" 은 변호사 계정 목록에 따라 본인이 담당하는 법원의 사건만 크롤링 하도록 변경. 변호사 계정 목록을 Google Cloud Secret Manager으로 관리.
  - [ ] "국선 크롤링 테스트 통합버젼 _20250707_최종본.ipynb" 에서 이용할 계정, 비밀번호 관리
    - 추후 관심사건에 등록되지 않은 것을 전자소송 홈페이지에 관심사건으로 등록하기 위함.
  

"전자소송" 관심사건 등록 계정  id:lawyer87 pw:kkcho0904! 

좋습니다 👍
“전자소송(e-litigation)“과 “국선사건(public defender / court-appointed case)“을 코드에서 쓰기 좋은 영어 변수명으로 추천드릴게요.

⸻

1. 전자소송 (Electronic Litigation)
	•	eLitigationCase
	•	electronicLitigation
	•	eFilingCase (미국 등에서는 전자소송을 e-filing이라 부름)
	•	litigationOnline
	•	courtEfiling

👉 추천: eLitigationCase
→ 짧고 직관적이며, 다른 개발자도 바로 이해하기 쉽습니다.

⸻

2. 국선사건 (Court-Appointed / Public Defender Case)
	•	courtAppointedCase
	•	publicDefenderCase
	•	appointedCounselCase
	•	legalAidCase
	•	defenseByCourt

👉 추천: courtAppointedCase
→ “법원이 지정한 사건”이라는 의미가 가장 명확하고 범용적으로 쓰입니다.

⸻

3. 조합 예시

만약 두 개를 한 곳에서 다뤄야 한다면:

eLitigationCase = {...}
courtAppointedCase = {...}

# 리스트나 dict 로 함께 관리할 수도 있음
cases = {
    "eLitigation": eLitigationCase,
    "courtAppointed": courtAppointedCase,
}


⸻

✅ 정리:
	•	전자소송 → eLitigationCase
	•	국선사건 → courtAppointedCase

⸻

원하시는 게 CamelCase 스타일인가요? 아니면 snake_case 스타일 (e_litigation_case, court_appointed_case)로 맞춰드릴까요?



---
