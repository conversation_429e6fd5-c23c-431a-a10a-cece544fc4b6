# Court Crawling System

A scalable Python application for crawling court case information, designed for deployment on Google Cloud Run. This system refactors existing Jupyter notebook crawlers into a modular, production-ready architecture following SOLID principles.

## Features

- **Modular Architecture**: Clean separation of concerns with abstract base classes
- **Multiple Crawlers**: Support for Public Defender and Electronic Litigation systems
- **Cloud-Native**: Designed for Google Cloud Run with proper scaling and resource management
- **Secret Management**: Secure credential storage using Google Cloud Secret Manager
- **Multiple Triggers**: HTTP endpoints, Cloud Scheduler, and manual execution
- **Robust Error Handling**: Comprehensive logging and error recovery
- **Google Sheets Integration**: Automatic data upload with batch processing and retry logic

## Architecture

```
src/
├── crawlers/           # Crawler implementations
│   ├── base.py        # Abstract base crawler
│   ├── public_defender.py
│   └── electronic_litigation.py
├── services/          # Core services
│   ├── config_manager.py
│   ├── secret_manager.py
│   ├── webdriver_manager.py
│   └── sheets_client.py
├── models/            # Data models
│   ├── case_data.py
│   └── config.py
├── utils/             # Utilities
│   ├── logger.py
│   ├── exceptions.py
│   └── date_utils.py
├── app.py             # FastAPI application
└── main.py            # CLI entry point
```

## Quick Start

### Prerequisites

- Python 3.12+
- uv package manager
- Google Cloud Project with billing enabled
- Docker (for deployment)

### Local Development

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd layer_crawling
   uv sync
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Run locally**:
   ```bash
   # Start the FastAPI server
   uv run python -m src.main start-server --debug

   # Or run crawlers directly
   uv run python -m src.main crawl-public-defender --days 7
   uv run python -m src.main crawl-electronic-litigation
   ```

### Cloud Deployment

1. **Setup Google Cloud**:
   ```bash
   # Set your project ID
   export GOOGLE_CLOUD_PROJECT="your-project-id"

   # Run setup script
   ./deploy/setup-secrets.sh
   ```

2. **Deploy to Cloud Run**:
   ```bash
   ./deploy/deploy.sh
   ```

3. **Setup scheduled jobs**:
   ```bash
   # Update SERVICE_URL in the script first
   ./deploy/setup-scheduler.sh
   ```

## Configuration

The system uses environment variables for configuration. Key settings:

### Required
- `GOOGLE_CLOUD_PROJECT`: Your Google Cloud project ID

### Optional
- `WEBDRIVER_HEADLESS`: Run browser in headless mode (default: true)
- `LOG_LEVEL`: Logging level (default: INFO)
- `API_KEY`: API key for authentication (optional)

See `.env.example` for all available options.

## Secret Management

The system uses Google Cloud Secret Manager for secure credential storage:

### Secrets Structure

1. **lawyer-accounts**: Lawyer account credentials
   ```json
   {
     "accounts": [
       {
         "username": "lawyer_username",
         "password": "lawyer_password",
         "assigned_courts": ["서울중앙지방법원", "서울동부지방법원"],
         "active": true
       }
     ]
   }
   ```

2. **electronic-litigation-credentials**: Electronic litigation credentials
   ```json
   {
     "username": "litigation_username",
     "password": "litigation_password"
   }
   ```

3. **google-sheets-credentials**: Service account key for Google Sheets access

## API Endpoints

### Health Check
```bash
GET /health
```

### Crawl Public Defender Cases
```bash
POST /crawl/public-defender
Content-Type: application/json

{
  "date_range_days": 7,
  "lawyer_username": "specific_lawyer" // optional
}
```

### Crawl Electronic Litigation Cases
```bash
POST /crawl/electronic-litigation
Content-Type: application/json

{}
```

### Crawl All Systems
```bash
POST /crawl/all
Content-Type: application/json

{
  "date_range_days": 7
}
```

## Scheduled Execution

The system supports automated execution via Google Cloud Scheduler:

- **Public Defender**: Daily at 9 AM KST
- **Electronic Litigation**: Daily at 10 AM KST
- **Combined Crawl**: Weekly on Mondays at 8 AM KST

## Development

### Running Tests
```bash
uv run pytest
```

### Code Formatting
```bash
uv run black src/
uv run ruff check src/
```

### Type Checking
```bash
uv run mypy src/
```

## Monitoring

The system includes comprehensive logging and monitoring:

- **Structured Logging**: JSON format with correlation IDs
- **Health Checks**: Built-in health endpoint for Cloud Run
- **Error Reporting**: Integration with Google Cloud Error Reporting
- **Metrics**: Custom metrics for crawling operations

## Troubleshooting

### Common Issues

1. **WebDriver Issues**:
   - Ensure Chrome is installed in the container
   - Check headless mode configuration
   - Verify timeout settings

2. **Authentication Failures**:
   - Verify credentials in Secret Manager
   - Check service account permissions
   - Ensure secrets are properly formatted

3. **Google Sheets Errors**:
   - Verify service account has access to sheets
   - Check API quotas and limits
   - Ensure proper JSON formatting

### Debugging

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
```

Check logs in Google Cloud Console:
```bash
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=court-crawling-system"
```

## Contributing

1. Follow SOLID principles
2. Add comprehensive tests
3. Update documentation
4. Use structured logging
5. Handle errors gracefully

## License

[Your License Here]